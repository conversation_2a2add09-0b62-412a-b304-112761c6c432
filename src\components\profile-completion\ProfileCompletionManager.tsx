/**
 * ProfileCompletionManager Component
 * Utility component for managing card visibility and testing
 */

import React, { useEffect, useState } from 'react';
import clsx from 'clsx';
import {
  useProfileCompletion,
  useProfileCompletionCache,
  useProfileCompletionInsights,
} from '../../hooks/useProfileCompletion';
import { ProfileCompletionService } from '../../services/profile-completion.service';
import { ProfileCompletionManagerProps } from '../../types/profile-completion';

const ProfileCompletionManager: React.FC<ProfileCompletionManagerProps> = ({
  children,
  resetOnMount = false,
}) => {
  const [showDebugPanel, setShowDebugPanel] = useState(false);
  const { completion, isLoading, error, refetch } = useProfileCompletion();
  const { clearCache, clearAllCache, getCacheStats } = useProfileCompletionCache();
  const insights = useProfileCompletionInsights();
  const [cacheStats, setCacheStats] = useState<any>(null);

  // Reset dismissal state on mount if requested
  useEffect(() => {
    if (resetOnMount) {
      ProfileCompletionService.resetCardDismissal();
    }
  }, [resetOnMount]);

  // Update cache stats
  useEffect(() => {
    const updateStats = () => {
      setCacheStats(getCacheStats());
    };
    
    updateStats();
    const interval = setInterval(updateStats, 5000); // Update every 5 seconds
    
    return () => clearInterval(interval);
  }, [getCacheStats]);

  const handleResetDismissal = () => {
    ProfileCompletionService.resetCardDismissal();
    window.location.reload(); // Refresh to show the card again
  };

  const handleClearCache = () => {
    clearCache();
    refetch();
  };

  const handleClearAllCache = () => {
    clearAllCache();
    refetch();
  };

  const handleRefresh = () => {
    refetch();
  };

  // Development mode check
  const isDevelopment = process.env.NODE_ENV === 'development';

  return (
    <div className="relative">
      {children}
      
      {/* Debug Panel Toggle (Development Only) */}
      {isDevelopment && (
        <button
          onClick={() => setShowDebugPanel(!showDebugPanel)}
          className="fixed bottom-4 right-4 bg-purple-600 text-white p-2 rounded-full shadow-lg hover:bg-purple-700 transition-colors z-50"
          title="Profile Completion Debug Panel"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
        </button>
      )}

      {/* Debug Panel */}
      {isDevelopment && showDebugPanel && (
        <div className="fixed bottom-16 right-4 bg-white border border-gray-300 rounded-lg shadow-xl p-4 w-80 max-h-96 overflow-y-auto z-50">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Profile Completion Debug</h3>
            <button
              onClick={() => setShowDebugPanel(false)}
              className="text-gray-400 hover:text-gray-600"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* Status */}
          <div className="mb-4">
            <h4 className="text-sm font-medium text-gray-700 mb-2">Status</h4>
            <div className="space-y-1 text-xs">
              <div className={clsx('flex justify-between', {
                'text-green-600': !isLoading,
                'text-yellow-600': isLoading,
              })}>
                <span>Loading:</span>
                <span>{isLoading ? 'Yes' : 'No'}</span>
              </div>
              <div className={clsx('flex justify-between', {
                'text-red-600': error,
                'text-green-600': !error,
              })}>
                <span>Error:</span>
                <span>{error ? 'Yes' : 'No'}</span>
              </div>
              <div className="flex justify-between">
                <span>Card Dismissed:</span>
                <span>{ProfileCompletionService.isCardDismissed() ? 'Yes' : 'No'}</span>
              </div>
            </div>
          </div>

          {/* Completion Data */}
          {completion && (
            <div className="mb-4">
              <h4 className="text-sm font-medium text-gray-700 mb-2">Completion Data</h4>
              <div className="space-y-1 text-xs">
                <div className="flex justify-between">
                  <span>Overall:</span>
                  <span className="font-medium">{completion.overallPercentage}%</span>
                </div>
                <div className="flex justify-between">
                  <span>Complete:</span>
                  <span>{completion.overallCompleted ? 'Yes' : 'No'}</span>
                </div>
                <div className="flex justify-between">
                  <span>Next Steps:</span>
                  <span>{completion.nextSteps.length}</span>
                </div>
                <div className="flex justify-between">
                  <span>Critical Missing:</span>
                  <span>{completion.criticalMissing.length}</span>
                </div>
              </div>
            </div>
          )}

          {/* Insights */}
          {insights && (
            <div className="mb-4">
              <h4 className="text-sm font-medium text-gray-700 mb-2">Insights</h4>
              <div className="space-y-1 text-xs">
                <div className="flex justify-between">
                  <span>Completed Sections:</span>
                  <span>{insights.completedSections}/{insights.totalSections}</span>
                </div>
                <div className="flex justify-between">
                  <span>Est. Time:</span>
                  <span>{insights.estimatedCompletionTime}min</span>
                </div>
                <div className="flex justify-between">
                  <span>Near Completion:</span>
                  <span>{insights.isNearCompletion ? 'Yes' : 'No'}</span>
                </div>
                <div className="flex justify-between">
                  <span>Perfect:</span>
                  <span>{insights.isPerfect ? 'Yes' : 'No'}</span>
                </div>
              </div>
            </div>
          )}

          {/* Cache Stats */}
          {cacheStats && (
            <div className="mb-4">
              <h4 className="text-sm font-medium text-gray-700 mb-2">Cache Stats</h4>
              <div className="space-y-1 text-xs">
                <div className="flex justify-between">
                  <span>Cache Size:</span>
                  <span>{cacheStats.size}</span>
                </div>
                <div className="flex justify-between">
                  <span>Entries:</span>
                  <span>{cacheStats.entries.length}</span>
                </div>
              </div>
            </div>
          )}

          {/* Actions */}
          <div className="space-y-2">
            <button
              onClick={handleResetDismissal}
              className="w-full px-3 py-2 bg-blue-600 text-white text-xs rounded-md hover:bg-blue-700 transition-colors"
            >
              Reset Card Dismissal
            </button>
            <button
              onClick={handleRefresh}
              className="w-full px-3 py-2 bg-green-600 text-white text-xs rounded-md hover:bg-green-700 transition-colors"
            >
              Refresh Data
            </button>
            <button
              onClick={handleClearCache}
              className="w-full px-3 py-2 bg-yellow-600 text-white text-xs rounded-md hover:bg-yellow-700 transition-colors"
            >
              Clear Cache
            </button>
            <button
              onClick={handleClearAllCache}
              className="w-full px-3 py-2 bg-red-600 text-white text-xs rounded-md hover:bg-red-700 transition-colors"
            >
              Clear All Cache
            </button>
          </div>

          {/* Error Details */}
          {error && (
            <div className="mt-4 p-2 bg-red-50 border border-red-200 rounded text-xs">
              <div className="font-medium text-red-800 mb-1">Error Details:</div>
              <div className="text-red-600">{error.message}</div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default ProfileCompletionManager;
