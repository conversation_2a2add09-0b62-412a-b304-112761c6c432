/**
 * Services Integration Tests
 * 
 * End-to-end tests for the complete services workflow including:
 * - Services page rendering
 * - Creating, editing, and deleting services
 * - Category management
 * - Error scenarios
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { BrowserRouter } from 'react-router';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import ServicesManagement from '../../pages/Services/ServicesManagement';
import { Service, ServiceCategory } from '../../types';

// Mock the services hooks
const mockServices: Service[] = [
  {
    id: 1,
    sProviderId: 1,
    title: 'General Consultation',
    duration: 30,
    price: 2500,
    pointsRequirements: 1,
    isPublic: true,
    deliveryType: 'at_location',
    servedRegions: [],
    description: 'Standard medical consultation',
    color: '#4CAF50',
    acceptOnline: true,
    acceptNew: true,
    notificationOn: true,
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z',
  },
  {
    id: 2,
    sProviderId: 1,
    title: 'Home Visit',
    duration: 60,
    price: 5000,
    pointsRequirements: 2,
    isPublic: true,
    deliveryType: 'at_customer',
    servedRegions: ['16', '31', '42'],
    description: 'Medical consultation at patient home',
    color: '#2196F3',
    acceptOnline: true,
    acceptNew: true,
    notificationOn: true,
    createdAt: '2024-01-15T11:00:00Z',
    updatedAt: '2024-01-15T11:00:00Z',
  },
];

const mockCategories: ServiceCategory[] = [
  { id: 1, title: 'Medical Consultations', sProviderId: 1 },
  { id: 2, title: 'Diagnostic Services', sProviderId: 1 },
];

vi.mock('../../hooks/useServices', () => ({
  useServices: () => ({
    data: mockServices,
    isLoading: false,
    error: null,
  }),
  useServiceCategories: () => ({
    data: mockCategories,
    isLoading: false,
    error: null,
  }),
  useDeleteService: () => ({
    mutateAsync: vi.fn(),
    isPending: false,
  }),
  useCreateService: () => ({
    mutateAsync: vi.fn(),
    isPending: false,
    error: null,
  }),
  useUpdateService: () => ({
    mutateAsync: vi.fn(),
    isPending: false,
    error: null,
  }),
  useCreateServiceCategory: () => ({
    mutateAsync: vi.fn(),
    isPending: false,
    error: null,
  }),
  useUpdateServiceCategory: () => ({
    mutateAsync: vi.fn(),
    isPending: false,
    error: null,
  }),
  useDeleteServiceCategory: () => ({
    mutateAsync: vi.fn(),
    isPending: false,
  }),
}));

// Mock react-hot-toast
vi.mock('react-hot-toast', () => ({
  default: {
    success: vi.fn(),
    error: vi.fn(),
  },
}));

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });
  
  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        {children}
      </BrowserRouter>
    </QueryClientProvider>
  );
};

describe('Services Integration', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Services Page Rendering', () => {
    it('renders services management page correctly', () => {
      render(<ServicesManagement />, { wrapper: createWrapper() });

      expect(screen.getByText('Services Management')).toBeInTheDocument();
      expect(screen.getByText('Manage your services, pricing, and categories')).toBeInTheDocument();
      expect(screen.getByText('Add New Service')).toBeInTheDocument();
      expect(screen.getByText('Manage Categories')).toBeInTheDocument();
    });

    it('displays services list correctly', () => {
      render(<ServicesManagement />, { wrapper: createWrapper() });

      expect(screen.getByText('General Consultation')).toBeInTheDocument();
      expect(screen.getByText('Home Visit')).toBeInTheDocument();
      expect(screen.getByText('30m')).toBeInTheDocument();
      expect(screen.getByText('60m')).toBeInTheDocument();
    });

    it('shows filters section', () => {
      render(<ServicesManagement />, { wrapper: createWrapper() });

      expect(screen.getByText('Category')).toBeInTheDocument();
      expect(screen.getByText('Delivery Type')).toBeInTheDocument();
      expect(screen.getByText('Visibility')).toBeInTheDocument();
      expect(screen.getByText('Search')).toBeInTheDocument();
    });
  });

  describe('Service Creation Workflow', () => {
    it('opens create service modal when button is clicked', async () => {
      const user = userEvent.setup();
      render(<ServicesManagement />, { wrapper: createWrapper() });

      const addButton = screen.getByText('Add New Service');
      await user.click(addButton);

      await waitFor(() => {
        expect(screen.getByText('Create New Service')).toBeInTheDocument();
      });
    });

    it('displays service form fields in create modal', async () => {
      const user = userEvent.setup();
      render(<ServicesManagement />, { wrapper: createWrapper() });

      const addButton = screen.getByText('Add New Service');
      await user.click(addButton);

      await waitFor(() => {
        expect(screen.getByLabelText(/Service Name/)).toBeInTheDocument();
        expect(screen.getByLabelText(/Duration/)).toBeInTheDocument();
        expect(screen.getByLabelText(/Price/)).toBeInTheDocument();
        expect(screen.getByLabelText(/Points Requirements/)).toBeInTheDocument();
        expect(screen.getByLabelText(/Delivery Type/)).toBeInTheDocument();
      });
    });
  });

  describe('Service Editing Workflow', () => {
    it('opens edit modal when edit button is clicked', async () => {
      const user = userEvent.setup();
      render(<ServicesManagement />, { wrapper: createWrapper() });

      // Find the first edit button (there should be one for each service)
      const editButtons = screen.getAllByText('Edit');
      await user.click(editButtons[0]);

      await waitFor(() => {
        expect(screen.getByText('Edit Service')).toBeInTheDocument();
        expect(screen.getByDisplayValue('General Consultation')).toBeInTheDocument();
      });
    });
  });

  describe('Service Deletion Workflow', () => {
    it('shows confirmation dialog when delete button is clicked', async () => {
      const user = userEvent.setup();
      
      // Mock window.confirm
      const confirmSpy = vi.spyOn(window, 'confirm').mockReturnValue(true);
      
      render(<ServicesManagement />, { wrapper: createWrapper() });

      const deleteButtons = screen.getAllByText('Delete');
      await user.click(deleteButtons[0]);

      expect(confirmSpy).toHaveBeenCalledWith('Are you sure you want to delete this service?');
    });
  });

  describe('Category Management Workflow', () => {
    it('opens category manager when button is clicked', async () => {
      const user = userEvent.setup();
      render(<ServicesManagement />, { wrapper: createWrapper() });

      const manageCategoriesButton = screen.getByText('Manage Categories');
      await user.click(manageCategoriesButton);

      await waitFor(() => {
        expect(screen.getByText('Manage Service Categories')).toBeInTheDocument();
        expect(screen.getByText('Create and organize your service categories')).toBeInTheDocument();
      });
    });

    it('displays existing categories in manager', async () => {
      const user = userEvent.setup();
      render(<ServicesManagement />, { wrapper: createWrapper() });

      const manageCategoriesButton = screen.getByText('Manage Categories');
      await user.click(manageCategoriesButton);

      await waitFor(() => {
        expect(screen.getByText('Medical Consultations')).toBeInTheDocument();
        expect(screen.getByText('Diagnostic Services')).toBeInTheDocument();
      });
    });
  });

  describe('Filtering Functionality', () => {
    it('filters services by search term', async () => {
      const user = userEvent.setup();
      render(<ServicesManagement />, { wrapper: createWrapper() });

      const searchInput = screen.getByPlaceholderText('Search services...');
      await user.type(searchInput, 'consultation');

      // In a real implementation, this would filter the displayed services
      expect(searchInput).toHaveValue('consultation');
    });

    it('filters services by delivery type', async () => {
      const user = userEvent.setup();
      render(<ServicesManagement />, { wrapper: createWrapper() });

      const deliverySelect = screen.getByDisplayValue('All Delivery Types');
      await user.selectOptions(deliverySelect, 'at_location');

      expect(deliverySelect).toHaveValue('at_location');
    });

    it('filters services by visibility', async () => {
      const user = userEvent.setup();
      render(<ServicesManagement />, { wrapper: createWrapper() });

      const visibilitySelect = screen.getByDisplayValue('All Services');
      await user.selectOptions(visibilitySelect, 'public');

      expect(visibilitySelect).toHaveValue('public');
    });
  });

  describe('Error Scenarios', () => {
    it('displays error message when services fail to load', () => {
      // Mock the hook to return an error
      vi.mocked(require('../../hooks/useServices').useServices).mockReturnValue({
        data: null,
        isLoading: false,
        error: { message: 'Failed to load services' },
      });

      render(<ServicesManagement />, { wrapper: createWrapper() });

      expect(screen.getByText('Failed to load services')).toBeInTheDocument();
    });

    it('shows loading state while services are being fetched', () => {
      // Mock the hook to return loading state
      vi.mocked(require('../../hooks/useServices').useServices).mockReturnValue({
        data: null,
        isLoading: true,
        error: null,
      });

      render(<ServicesManagement />, { wrapper: createWrapper() });

      expect(screen.getByText('Loading services...')).toBeInTheDocument();
    });
  });
});
