import React, { useState } from 'react';
import PageBreadcrumb from "../../components/common/PageBreadCrumb";
import PageMeta from "../../components/common/PageMeta";
import Button from "../../components/ui/button/Button";
import { Modal } from "../../components/ui/modal";
import { useModal } from "../../hooks/useModal";
import { ErrorDisplay } from "../../components/error";
import { 
  useProviderCustomers, 
  useDeactivateCustomerRelationship 
} from "../../hooks/useProviderCustomers";
import ProviderCustomerForm from "../../components/customers/ProviderCustomerForm";
import ProviderCustomerCard from "../../components/customers/ProviderCustomerCard";
import ProviderCustomerDetails from "../../components/customers/ProviderCustomerDetails";
import CustomerStatistics from "../../components/customers/CustomerStatistics";
import CustomerExport from "../../components/customers/CustomerExport";
import CustomerQuickActions from "../../components/customers/CustomerQuickActions";
import { ProviderCustomer, ProviderCustomerFilters } from "../../types/provider-customer";
import { UserGroupIcon, MagnifyingGlassIcon } from "../../icons";

export default function CustomersManagement() {
  const [editingCustomer, setEditingCustomer] = useState<ProviderCustomer | null>(null);
  const [selectedCustomer, setSelectedCustomer] = useState<ProviderCustomer | null>(null);
  const [filters, setFilters] = useState<ProviderCustomerFilters>({});
  const [modalType, setModalType] = useState<'form' | 'details' | 'export' | null>(null);
  const [viewMode, setViewMode] = useState<'cards' | 'table'>('cards');
  const [showStatistics, setShowStatistics] = useState(true);
  const { isOpen, openModal, closeModal } = useModal();

  const { data: customersResponse, isLoading, error } = useProviderCustomers(filters);
  const deactivateCustomerMutation = useDeactivateCustomerRelationship();

  // Ensure customers is always an array
  const customers = Array.isArray(customersResponse?.customers) ? customersResponse.customers : [];
  const pagination = customersResponse?.pagination;

  const handleCreateCustomer = () => {
    setEditingCustomer(null);
    setModalType('form');
    openModal();
  };

  const handleEditCustomer = (customer: ProviderCustomer) => {
    setEditingCustomer(customer);
    setModalType('form');
    openModal();
  };

  const handleViewCustomer = (customer: ProviderCustomer) => {
    setSelectedCustomer(customer);
    setModalType('details');
    openModal();
  };

  const handleDeleteCustomer = async (customer: ProviderCustomer) => {
    if (window.confirm(`Are you sure you want to remove ${customer.firstName} ${customer.lastName} from your customer list? This will deactivate the relationship but preserve all data.`)) {
      try {
        await deactivateCustomerMutation.mutateAsync(customer.id);
      } catch (error) {
        // Error handled by mutation
      }
    }
  };

  const handleExportCustomers = () => {
    setModalType('export');
    openModal();
  };

  const handleCloseModal = () => {
    setEditingCustomer(null);
    setSelectedCustomer(null);
    setModalType(null);
    closeModal();
  };

  const handleSuccess = () => {
    handleCloseModal();
  };

  const handleFilterChange = (newFilters: Partial<ProviderCustomerFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  };

  const clearFilters = () => {
    setFilters({});
  };

  const hasActiveFilters = Object.keys(filters).some(key => 
    filters[key as keyof ProviderCustomerFilters] !== undefined && 
    filters[key as keyof ProviderCustomerFilters] !== ''
  );

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brand-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <ErrorDisplay
          error={error}
          title="Failed to load customers"
          variant="card"
          showRetry
          onRetry={() => window.location.reload()}
        />
      </div>
    );
  }

  return (
    <>
      <PageMeta
        title="Customer Management | Provider Dashboard"
        description="Manage your customer relationships, track interactions, and maintain customer records"
      />
      <PageBreadcrumb pageTitle="Customer Management" />

      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              Customer Management
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              Manage your customer relationships and track their appointment history
            </p>
          </div>
          <div className="flex flex-col sm:flex-row gap-3">
            <Button
              onClick={handleCreateCustomer}
              size="sm"
            >
              Add New Customer
            </Button>
          </div>
        </div>

        {/* Customer Statistics */}
        {showStatistics && customers.length > 0 && (
          <CustomerStatistics customers={customers} />
        )}

        {/* Quick Actions and Filters */}
        <CustomerQuickActions
          customers={customers}
          onCreateCustomer={handleCreateCustomer}
          onExportCustomers={handleExportCustomers}
          onFilterChange={handleFilterChange}
          currentFilters={filters}
        />

        {/* View Mode Toggle */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">View:</span>
            <button
              onClick={() => setViewMode('cards')}
              className={`p-2 rounded-lg transition-colors ${
                viewMode === 'cards'
                  ? 'bg-brand-100 text-brand-600 dark:bg-brand-900/20 dark:text-brand-400'
                  : 'text-gray-400 hover:text-gray-600 dark:hover:text-gray-300'
              }`}
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
              </svg>
            </button>
            <button
              onClick={() => setViewMode('table')}
              className={`p-2 rounded-lg transition-colors ${
                viewMode === 'table'
                  ? 'bg-brand-100 text-brand-600 dark:bg-brand-900/20 dark:text-brand-400'
                  : 'text-gray-400 hover:text-gray-600 dark:hover:text-gray-300'
              }`}
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 10h16M4 14h16M4 18h16" />
              </svg>
            </button>
          </div>

          <div className="flex items-center space-x-2">
            <button
              onClick={() => setShowStatistics(!showStatistics)}
              className="text-sm text-brand-600 dark:text-brand-400 hover:text-brand-700 dark:hover:text-brand-300"
            >
              {showStatistics ? 'Hide' : 'Show'} Statistics
            </button>
          </div>
        </div>

        {/* Customers List */}
        {customers && customers.length > 0 ? (
          <div className={viewMode === 'cards' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6' : 'space-y-4'}>
            {customers.map((customer) => (
              <ProviderCustomerCard
                key={customer.id}
                customer={customer}
                viewMode={viewMode}
                onView={() => handleViewCustomer(customer)}
                onEdit={() => handleEditCustomer(customer)}
                onDelete={() => handleDeleteCustomer(customer)}
                isDeleting={deactivateCustomerMutation.isPending}
              />
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <div className="mx-auto h-24 w-24 text-gray-400 mb-4">
              <UserGroupIcon className="w-full h-full" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              No customers found
            </h3>
            <p className="text-gray-500 dark:text-gray-400 mb-6">
              {hasActiveFilters
                ? "No customers match your current filters. Try adjusting your search criteria."
                : "Get started by adding your first customer to build relationships."
              }
            </p>
            {!hasActiveFilters && (
              <Button onClick={handleCreateCustomer}>
                Add Your First Customer
              </Button>
            )}
          </div>
        )}

        {/* Pagination */}
        {pagination && pagination.totalPages > 1 && (
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-700 dark:text-gray-300">
              Showing {((pagination.page - 1) * pagination.limit) + 1} to {Math.min(pagination.page * pagination.limit, pagination.total)} of {pagination.total} customers
            </div>
            <div className="flex items-center space-x-2">
              <Button
                onClick={() => handleFilterChange({ page: pagination.page - 1 })}
                disabled={!pagination.hasPrev}
                variant="outline"
                size="sm"
              >
                Previous
              </Button>
              <span className="text-sm text-gray-700 dark:text-gray-300">
                Page {pagination.page} of {pagination.totalPages}
              </span>
              <Button
                onClick={() => handleFilterChange({ page: pagination.page + 1 })}
                disabled={!pagination.hasNext}
                variant="outline"
                size="sm"
              >
                Next
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* Customer Form Modal */}
      <Modal
        isOpen={isOpen && modalType === 'form'}
        onClose={handleCloseModal}
        className="max-w-[800px] p-0"
        showCloseButton={false}
      >
        <ProviderCustomerForm
          customer={editingCustomer}
          onClose={handleCloseModal}
          onSuccess={handleSuccess}
        />
      </Modal>

      {/* Customer Details Modal */}
      <Modal
        isOpen={isOpen && modalType === 'details'}
        onClose={handleCloseModal}
        className="max-w-[1000px] p-0"
        showCloseButton={false}
      >
        {selectedCustomer && (
          <ProviderCustomerDetails
            customer={selectedCustomer}
            onClose={handleCloseModal}
            onEdit={() => {
              setEditingCustomer(selectedCustomer);
              setSelectedCustomer(null);
              setModalType('form');
            }}
          />
        )}
      </Modal>

      {/* Customer Export Modal */}
      <CustomerExport
        customers={customers}
        isOpen={isOpen && modalType === 'export'}
        onClose={handleCloseModal}
      />
    </>
  );
}
