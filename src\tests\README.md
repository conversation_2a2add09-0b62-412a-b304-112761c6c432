# Services Testing Documentation

This directory contains comprehensive tests for the services functionality in the provider dashboard application.

## Test Structure

### Unit Tests
- **ServiceForm.test.tsx**: Tests for the ServiceForm component
- **ServiceService.test.ts**: Tests for the ServiceService API class
- **useServices.test.ts**: Tests for React Query hooks

### Integration Tests
- **ServicesIntegration.test.tsx**: End-to-end workflow tests

## Test Coverage

### ServiceForm Component Tests
- ✅ Form rendering (create/edit modes)
- ✅ Form validation (required fields, business rules)
- ✅ User interactions (form submission, field changes)
- ✅ Error handling (API errors, validation errors)
- ✅ Delivery type validation (served regions requirement)

### ServiceService API Tests
- ✅ CRUD operations (create, read, update, delete)
- ✅ Service categories management
- ✅ API response handling
- ✅ Error scenarios (network errors, validation errors)
- ✅ Request parameter passing

### React Query Hooks Tests
- ✅ Data fetching (services, categories)
- ✅ Mutations (create, update, delete)
- ✅ Error handling
- ✅ Query invalidation
- ✅ Loading states

### Integration Tests
- ✅ Complete page rendering
- ✅ Service creation workflow
- ✅ Service editing workflow
- ✅ Service deletion workflow
- ✅ Category management workflow
- ✅ Filtering functionality
- ✅ Error scenarios

## Running Tests

### Prerequisites
Install testing dependencies:
```bash
npm install --save-dev vitest @testing-library/react @testing-library/jest-dom @testing-library/user-event jsdom
```

### Test Commands
```bash
# Run all tests
npm test

# Run tests with UI
npm run test:ui

# Run tests with coverage
npm run test:coverage

# Run tests once (CI mode)
npm run test:run

# Run specific test file
npx vitest ServiceForm.test.tsx

# Run tests in watch mode
npx vitest --watch
```

## Test Configuration

### Vitest Configuration
- **Environment**: jsdom (for DOM testing)
- **Setup**: Global test utilities and mocks
- **Coverage**: v8 provider with HTML/JSON reports

### Mocks
- **API Client**: Mocked for isolated testing
- **React Query**: Custom wrapper for testing
- **Toast Notifications**: Mocked to prevent side effects
- **Browser APIs**: localStorage, sessionStorage, matchMedia

## Business Rules Tested

### Service Validation
- ✅ Title: Required, 1-255 characters
- ✅ Duration: 1-1440 minutes (24 hours max)
- ✅ Price: Non-negative, max 999,999.99
- ✅ Points: Non-negative integer, default 1
- ✅ Color: Valid hex format (#RRGGBB)
- ✅ Delivery Type: at_location, at_customer, both
- ✅ Served Regions: Required for customer delivery

### API Response Handling
- ✅ Wrapped responses: {success, data, message}
- ✅ Error responses: Proper error extraction
- ✅ Network errors: Graceful handling
- ✅ Validation errors: Field-specific messages

### User Experience
- ✅ Loading states during operations
- ✅ Success/error notifications
- ✅ Form validation feedback
- ✅ Modal interactions
- ✅ Filter functionality

## Test Data

### Mock Service
```typescript
{
  id: 1,
  sProviderId: 1,
  title: 'General Consultation',
  duration: 30,
  price: 2500,
  pointsRequirements: 1,
  isPublic: true,
  deliveryType: 'at_location',
  servedRegions: [],
  description: 'Standard medical consultation',
  color: '#4CAF50',
  acceptOnline: true,
  acceptNew: true,
  notificationOn: true,
  createdAt: '2024-01-15T10:00:00Z',
  updatedAt: '2024-01-15T10:00:00Z',
}
```

### Mock Service Category
```typescript
{
  id: 1,
  title: 'Medical Consultations',
  sProviderId: 1,
}
```

## Testing Best Practices

### Component Testing
1. Test user interactions, not implementation details
2. Use semantic queries (getByRole, getByLabelText)
3. Test error states and edge cases
4. Mock external dependencies

### API Testing
1. Test request/response handling
2. Verify correct parameters are passed
3. Test error scenarios
4. Mock network layer, not business logic

### Integration Testing
1. Test complete user workflows
2. Verify component interactions
3. Test state management
4. Use realistic test data

## Continuous Integration

### GitHub Actions Example
```yaml
name: Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm ci
      - run: npm run test:coverage
      - uses: codecov/codecov-action@v3
```

## Coverage Goals

- **Statements**: > 90%
- **Branches**: > 85%
- **Functions**: > 90%
- **Lines**: > 90%

## Future Test Enhancements

1. **E2E Tests**: Add Playwright/Cypress tests
2. **Visual Regression**: Add screenshot testing
3. **Performance**: Add performance benchmarks
4. **Accessibility**: Add a11y testing
5. **Mobile**: Add responsive design tests
