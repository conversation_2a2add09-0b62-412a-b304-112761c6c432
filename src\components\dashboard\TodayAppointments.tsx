import React from 'react';
import { useNavigate } from 'react-router';
import Button from '../ui/button/Button';
import { formatLocalTime } from '../../utils/timezone';
import { useTodayAppointments } from '../../hooks/useDashboard';

const TodayAppointments: React.FC = () => {
  const navigate = useNavigate();
  const { data: todayData, isLoading, error } = useTodayAppointments();

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'InProgress':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'completed':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const appointments = todayData?.appointments || [];

  const getNextAppointment = () => {
    const now = new Date();
    return appointments
      .filter(apt => new Date(apt.expectedAppointmentStartTime) > now)
      .sort((a, b) => new Date(a.expectedAppointmentStartTime).getTime() - new Date(b.expectedAppointmentStartTime).getTime())[0];
  };

  const nextAppointment = getNextAppointment();

  if (isLoading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 animate-pulse">
        <div className="flex items-center justify-between mb-4">
          <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/3"></div>
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-16"></div>
        </div>
        <div className="space-y-3">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-gray-200 dark:bg-gray-600 rounded-full"></div>
                <div>
                  <div className="h-4 bg-gray-200 dark:bg-gray-600 rounded w-24 mb-1"></div>
                  <div className="h-3 bg-gray-200 dark:bg-gray-600 rounded w-16"></div>
                </div>
              </div>
              <div className="text-right">
                <div className="h-4 bg-gray-200 dark:bg-gray-600 rounded w-12 mb-1"></div>
                <div className="h-3 bg-gray-200 dark:bg-gray-600 rounded w-16"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
        <div className="text-center">
          <div className="w-16 h-16 mx-auto mb-4 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center">
            <svg className="w-8 h-8 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            Error Loading Appointments
          </h3>
          <p className="text-gray-600 dark:text-gray-400">
            Unable to load today's appointments. Please try again.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          Today's Appointments
        </h3>
        <Button
          variant="outline"
          size="sm"
          onClick={() => navigate('/appointments')}
        >
          View All
        </Button>
      </div>

      {appointments.length === 0 ? (
        <div className="text-center py-8">
          <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
            <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
          </div>
          <p className="text-gray-600 dark:text-gray-400">No appointments today</p>
        </div>
      ) : (
        <div className="space-y-3">
          {/* Next Appointment Highlight */}
          {nextAppointment && (
            <div className="bg-brand-50 dark:bg-brand-900/20 border border-brand-200 dark:border-brand-800 rounded-lg p-3 mb-4">
              <div className="flex items-center gap-2 mb-2">
                <div className="w-2 h-2 bg-brand-500 rounded-full animate-pulse"></div>
                <span className="text-sm font-medium text-brand-700 dark:text-brand-300">Next Appointment</span>
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium text-gray-900 dark:text-white">
                    {nextAppointment.customer.firstName} {nextAppointment.customer.lastName}
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {nextAppointment.service.title}
                  </p>
                </div>
                <div className="text-right">
                  <p className="font-medium text-brand-700 dark:text-brand-300">
                    {formatLocalTime(nextAppointment.expectedAppointmentStartTime)}
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {nextAppointment.service.duration} min
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* All Appointments List */}
          <div className="space-y-2">
            {appointments.slice(0, 4).map((appointment) => (
              <div
                key={appointment.id}
                className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors cursor-pointer"
                onClick={() => navigate('/appointments')}
              >
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-medium text-sm">
                    {appointment.customer.firstName.charAt(0)}{appointment.customer.lastName.charAt(0)}
                  </div>
                  <div>
                    <p className="font-medium text-gray-900 dark:text-white text-sm">
                      {appointment.customer.firstName} {appointment.customer.lastName}
                    </p>
                    <p className="text-xs text-gray-600 dark:text-gray-400">
                      {appointment.service.title}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                    {formatLocalTime(appointment.expectedAppointmentStartTime)}
                  </p>
                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(appointment.status)}`}>
                    {appointment.status}
                  </span>
                </div>
              </div>
            ))}
          </div>

          {appointments.length > 4 && (
            <div className="text-center pt-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigate('/appointments')}
                className="text-brand-600 hover:text-brand-700"
              >
                +{appointments.length - 4} more appointments
              </Button>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default TodayAppointments;
