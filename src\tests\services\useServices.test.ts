/**
 * useServices Hooks Tests
 *
 * Tests for the services-related React Query hooks including:
 * - useServices
 * - useCreateService
 * - useUpdateService
 * - useDeleteService
 * - useServiceCategories
 */

import React from 'react';
import { renderHook, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { describe, it, expect, beforeEach, vi } from 'vitest';
import {
  useServices,
  useCreateService,
  useUpdateService,
  useDeleteService,
  useServiceCategories,
} from '../../hooks/useServices';
import { ServiceService } from '../../services/service.service';
import { Service, ServiceCreateRequest } from '../../types';

// Mock the ServiceService
vi.mock('../../services/service.service', () => ({
  ServiceService: {
    getServices: vi.fn(),
    createService: vi.fn(),
    updateService: vi.fn(),
    deleteService: vi.fn(),
    getServiceCategories: vi.fn(),
  },
}));

// Mock react-hot-toast
vi.mock('react-hot-toast', () => ({
  default: {
    success: vi.fn(),
    error: vi.fn(),
  },
}));

// Mock ErrorLogger
vi.mock('../../lib/error-utils', () => ({
  ErrorLogger: {
    log: vi.fn(),
  },
}));

const mockService: Service = {
  id: 1,
  sProviderId: 1,
  title: 'General Consultation',
  duration: 30,
  price: 2500,
  pointsRequirements: 1,
  isPublic: true,
  deliveryType: 'at_location',
  servedRegions: [],
  description: 'Standard medical consultation',
  color: '#4CAF50',
  acceptOnline: true,
  acceptNew: true,
  notificationOn: true,
  createdAt: '2024-01-15T10:00:00Z',
  updatedAt: '2024-01-15T10:00:00Z',
};

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });
  
  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

describe('useServices Hooks', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('useServices', () => {
    it('fetches services successfully', async () => {
      vi.mocked(ServiceService.getServices).mockResolvedValue([mockService]);

      const { result } = renderHook(() => useServices(), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(result.current.data).toEqual([mockService]);
      expect(ServiceService.getServices).toHaveBeenCalledWith(undefined);
    });

    it('passes filters correctly', async () => {
      vi.mocked(ServiceService.getServices).mockResolvedValue([mockService]);
      const filters = { isPublic: true };

      const { result } = renderHook(() => useServices(filters), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(ServiceService.getServices).toHaveBeenCalledWith(filters);
    });

    it('handles errors correctly', async () => {
      const error = new Error('Failed to fetch services');
      vi.mocked(ServiceService.getServices).mockRejectedValue(error);

      const { result } = renderHook(() => useServices(), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect(result.current.isError).toBe(true);
      });

      expect(result.current.error).toEqual(error);
    });
  });

  describe('useCreateService', () => {
    it('creates service successfully', async () => {
      vi.mocked(ServiceService.createService).mockResolvedValue(mockService);

      const { result } = renderHook(() => useCreateService(), {
        wrapper: createWrapper(),
      });

      const serviceData: ServiceCreateRequest = {
        title: 'New Service',
        duration: 60,
        price: 100,
        deliveryType: 'at_location',
        color: '#4CAF50',
      };

      await result.current.mutateAsync(serviceData);

      expect(ServiceService.createService).toHaveBeenCalledWith(serviceData);
    });

    it('handles creation errors', async () => {
      const error = new Error('Failed to create service');
      vi.mocked(ServiceService.createService).mockRejectedValue(error);

      const { result } = renderHook(() => useCreateService(), {
        wrapper: createWrapper(),
      });

      const serviceData: ServiceCreateRequest = {
        title: 'New Service',
        duration: 60,
        price: 100,
        deliveryType: 'at_location',
        color: '#4CAF50',
      };

      await expect(result.current.mutateAsync(serviceData)).rejects.toThrow(
        'Failed to create service'
      );
    });
  });

  describe('useUpdateService', () => {
    it('updates service successfully', async () => {
      vi.mocked(ServiceService.updateService).mockResolvedValue(mockService);

      const { result } = renderHook(() => useUpdateService(), {
        wrapper: createWrapper(),
      });

      const updateData = { id: 1, data: { title: 'Updated Service' } };

      await result.current.mutateAsync(updateData);

      expect(ServiceService.updateService).toHaveBeenCalledWith(1, { title: 'Updated Service' });
    });

    it('handles update errors', async () => {
      const error = new Error('Failed to update service');
      vi.mocked(ServiceService.updateService).mockRejectedValue(error);

      const { result } = renderHook(() => useUpdateService(), {
        wrapper: createWrapper(),
      });

      const updateData = { id: 1, data: { title: 'Updated Service' } };

      await expect(result.current.mutateAsync(updateData)).rejects.toThrow(
        'Failed to update service'
      );
    });
  });

  describe('useDeleteService', () => {
    it('deletes service successfully', async () => {
      vi.mocked(ServiceService.deleteService).mockResolvedValue(undefined);

      const { result } = renderHook(() => useDeleteService(), {
        wrapper: createWrapper(),
      });

      await result.current.mutateAsync(1);

      expect(ServiceService.deleteService).toHaveBeenCalledWith(1);
    });

    it('handles deletion errors', async () => {
      const error = new Error('Cannot delete service with active appointments');
      vi.mocked(ServiceService.deleteService).mockRejectedValue(error);

      const { result } = renderHook(() => useDeleteService(), {
        wrapper: createWrapper(),
      });

      await expect(result.current.mutateAsync(1)).rejects.toThrow(
        'Cannot delete service with active appointments'
      );
    });
  });

  describe('useServiceCategories', () => {
    const mockCategory = {
      id: 1,
      title: 'Medical Consultations',
      sProviderId: 1,
    };

    it('fetches service categories successfully', async () => {
      vi.mocked(ServiceService.getServiceCategories).mockResolvedValue([mockCategory]);

      const { result } = renderHook(() => useServiceCategories(), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(result.current.data).toEqual([mockCategory]);
      expect(ServiceService.getServiceCategories).toHaveBeenCalled();
    });

    it('handles category fetch errors', async () => {
      const error = new Error('Failed to fetch categories');
      vi.mocked(ServiceService.getServiceCategories).mockRejectedValue(error);

      const { result } = renderHook(() => useServiceCategories(), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect(result.current.isError).toBe(true);
      });

      expect(result.current.error).toEqual(error);
    });
  });

  describe('Query Invalidation', () => {
    it('invalidates queries after successful creation', async () => {
      vi.mocked(ServiceService.createService).mockResolvedValue(mockService);

      const queryClient = new QueryClient({
        defaultOptions: {
          queries: { retry: false },
          mutations: { retry: false },
        },
      });

      const invalidateQueriesSpy = vi.spyOn(queryClient, 'invalidateQueries');

      const wrapper = ({ children }: { children: React.ReactNode }) => (
        <QueryClientProvider client={queryClient}>
          {children}
        </QueryClientProvider>
      );

      const { result } = renderHook(() => useCreateService(), { wrapper });

      const serviceData: ServiceCreateRequest = {
        title: 'New Service',
        duration: 60,
        price: 100,
        deliveryType: 'at_location',
        color: '#4CAF50',
      };

      await result.current.mutateAsync(serviceData);

      expect(invalidateQueriesSpy).toHaveBeenCalledWith({ queryKey: ['services'] });
      expect(invalidateQueriesSpy).toHaveBeenCalledWith({ queryKey: ['service-stats'] });
    });
  });
});
