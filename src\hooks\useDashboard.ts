import { useQuery } from '@tanstack/react-query';
import { DashboardService } from '../services/dashboard.service';

/**
 * Hook for fetching dashboard metrics
 */
export const useDashboardMetrics = () => {
  return useQuery({
    queryKey: ['dashboard', 'metrics'],
    queryFn: () => DashboardService.getDashboardMetrics(),
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });
};

/**
 * Hook for fetching today's appointments
 */
export const useTodayAppointments = () => {
  return useQuery({
    queryKey: ['dashboard', 'today-appointments'],
    queryFn: () => DashboardService.getTodayAppointments(),
    staleTime: 1 * 60 * 1000, // 1 minute
    gcTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
    refetchInterval: 60000, // Refetch every minute
  });
};

/**
 * Hook for fetching revenue chart data
 */
export const useRevenueChart = (period: 'week' | 'month' = 'week') => {
  return useQuery({
    queryKey: ['dashboard', 'revenue-chart', period],
    queryFn: () => DashboardService.getRevenueChart(period),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
  });
};
