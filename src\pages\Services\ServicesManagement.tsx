import React, { useState } from 'react';
import PageBreadcrumb from "../../components/common/PageBreadCrumb";
import PageMeta from "../../components/common/PageMeta";
import Button from "../../components/ui/button/Button";
import { Modal } from "../../components/ui/modal";
import { useModal } from "../../hooks/useModal";
import { ErrorDisplay } from "../../components/error";
import ConfirmationDialog from "../../components/ui/confirmation/ConfirmationDialog";
import { useConfirmation } from "../../hooks/useConfirmation";
import { useServices, useServiceCategories, useDeleteService } from "../../hooks/useServices";
import ServiceForm from "../../components/services/ServiceForm";
import ServiceCard from "../../components/services/ServiceCard";
import ServiceCategoryManager from "../../components/services/ServiceCategoryManager";
import {
  CreateServiceButton,
  UsageWarningBanner
} from "../../components/subscription";
import { Service, ServiceFilters } from "../../types";

export default function ServicesManagement() {
  const [editingService, setEditingService] = useState<Service | null>(null);
  const [filters, setFilters] = useState<ServiceFilters>({});
  const [modalType, setModalType] = useState<'service' | 'category' | null>(null);
  const { isOpen, openModal, closeModal } = useModal();
  const confirmation = useConfirmation();

  const { data: services, isLoading, error } = useServices(filters);
  const { data: categories } = useServiceCategories();
  const deleteServiceMutation = useDeleteService();

  const handleCreateService = () => {
    setEditingService(null);
    setModalType('service');
    openModal();
  };

  const handleEditService = (service: Service) => {
    setEditingService(service);
    setModalType('service');
    openModal();
  };

  const handleDeleteService = async (id: number) => {
    // Find the service to get its title for the confirmation message
    const serviceToDelete = Array.isArray(services) ? services.find((s: Service) => s.id === id) : null;
    const serviceTitle = serviceToDelete?.title || 'this service';

    const confirmed = await confirmation.confirm({
      title: 'Delete Service',
      message: `Are you sure you want to delete "${serviceTitle}"? This action cannot be undone and will remove all associated data.`,
      confirmText: 'Delete Service',
      cancelText: 'Cancel',
      variant: 'danger'
    });

    if (confirmed) {
      try {
        await deleteServiceMutation.mutateAsync(id);
        confirmation.close();
      } catch (error) {
        confirmation.close();
        // Error handled by mutation
      }
    }
  };

  const handleOpenCategoryManager = () => {
    setModalType('category');
    openModal();
  };

  const handleCloseModal = () => {
    setEditingService(null);
    setModalType(null);
    closeModal();
  };

  const handleSuccess = () => {
    handleCloseModal();
  };

  const handleFilterChange = (newFilters: Partial<ServiceFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brand-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <ErrorDisplay
          error={error}
          title="Failed to load services"
          variant="card"
          showRetry
          onRetry={() => window.location.reload()}
        />
      </div>
    );
  }

  return (
    <>
      <PageMeta
        title="Services Management | Provider Dashboard"
        description="Manage your services, pricing, and categories"
      />
      <PageBreadcrumb pageTitle="Services" />

      <div className="space-y-6">
        {/* Usage Warning Banner */}
        {/* <UsageWarningBanner /> */}

        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              Services Management
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              Manage your services, pricing, and categories
            </p>
          </div>
          <div className="flex flex-col sm:flex-row gap-3">
            <Button
              onClick={handleOpenCategoryManager}
              variant="outline"
              size="sm"
            >
              Manage Categories
            </Button>
            <CreateServiceButton
              onCreateService={handleCreateService}
              size="sm"
            />
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Category
              </label>
              <select
                value={filters.categoryId || ''}
                onChange={(e) => handleFilterChange({ 
                  categoryId: e.target.value ? Number(e.target.value) : undefined 
                })}
                className="w-full h-10 rounded-lg border border-gray-300 bg-transparent px-3 py-2 text-sm text-gray-800 focus:border-brand-300 focus:outline-none focus:ring-2 focus:ring-brand-500/10 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
              >
                <option value="">All Categories</option>
                {categories && Array.isArray(categories) && categories.map((category: any) => (
                  <option key={category.id} value={category.id}>
                    {category.title}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Delivery Type
              </label>
              <select
                value={filters.deliveryType || ''}
                onChange={(e) => handleFilterChange({ 
                  deliveryType: e.target.value as any || undefined 
                })}
                className="w-full h-10 rounded-lg border border-gray-300 bg-transparent px-3 py-2 text-sm text-gray-800 focus:border-brand-300 focus:outline-none focus:ring-2 focus:ring-brand-500/10 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
              >
                <option value="">All Types</option>
                <option value="at_location">At Location</option>
                <option value="at_customer">At Customer</option>
                <option value="both">Both</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Status
              </label>
              <select
                value={filters.isPublic !== undefined ? (filters.isPublic ? 'public' : 'private') : ''}
                onChange={(e) => handleFilterChange({ 
                  isPublic: e.target.value === 'public' ? true : e.target.value === 'private' ? false : undefined 
                })}
                className="w-full h-10 rounded-lg border border-gray-300 bg-transparent px-3 py-2 text-sm text-gray-800 focus:border-brand-300 focus:outline-none focus:ring-2 focus:ring-brand-500/10 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
              >
                <option value="">All Services</option>
                <option value="public">Public</option>
                <option value="private">Private</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Search
              </label>
              <input
                type="text"
                placeholder="Search services..."
                value={filters.search || ''}
                onChange={(e) => handleFilterChange({ search: e.target.value || undefined })}
                className="w-full h-10 rounded-lg border border-gray-300 bg-transparent px-3 py-2 text-sm text-gray-800 placeholder:text-gray-400 focus:border-brand-300 focus:outline-none focus:ring-2 focus:ring-brand-500/10 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder:text-gray-500"
              />
            </div>
          </div>
        </div>

        {/* Services Grid */}
        {services && Array.isArray(services) && services.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {services.map((service: Service) => (
              <ServiceCard
                key={service.id}
                service={service}
                onEdit={() => handleEditService(service)}
                onDelete={() => handleDeleteService(service.id)}
                isDeleting={deleteServiceMutation.isPending}
              />
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <div className="mx-auto h-24 w-24 text-gray-400 mb-4">
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              No services found
            </h3>
            <p className="text-gray-500 dark:text-gray-400 mb-6">
              {Object.keys(filters).length > 0 
                ? "No services match your current filters. Try adjusting your search criteria."
                : "Get started by creating your first service."
              }
            </p>
            {Object.keys(filters).length === 0 && (
              <Button onClick={handleCreateService}>
                Create Your First Service
              </Button>
            )}
          </div>
        )}
      </div>

      {/* Service Modal */}
      <Modal
        isOpen={isOpen}
        onClose={handleCloseModal}
        className="max-w-[800px] p-0"
      >
        {true ? (
          <ServiceForm
            service={editingService}
            onClose={handleCloseModal}
            onSuccess={handleSuccess}
          />
        ) : modalType === 'category' ? (
          <ServiceCategoryManager
            onClose={handleCloseModal}
          />
        ) : null}
      </Modal>

      {/* Delete Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={confirmation.isOpen}
        onClose={confirmation.cancel}
        onConfirm={confirmation.onConfirm}
        title={confirmation.title}
        message={confirmation.message}
        confirmText={confirmation.confirmText}
        cancelText={confirmation.cancelText}
        variant={confirmation.variant}
        isLoading={confirmation.isLoading}
      />
    </>
  );
}
