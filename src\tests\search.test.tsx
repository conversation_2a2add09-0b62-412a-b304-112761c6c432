import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router';
import GlobalSearchInput from '../components/search/GlobalSearchInput';
import { useGlobalSearch } from '../hooks/useGlobalSearch';

// Mock the useGlobalSearch hook
vi.mock('../hooks/useGlobalSearch');

const mockUseGlobalSearch = vi.mocked(useGlobalSearch);

const mockSearchResults = [
  {
    id: 'dashboard',
    title: 'Dashboard',
    description: 'Overview of your business metrics',
    path: '/',
    category: 'page' as const,
    icon: '📊',
    keywords: ['home', 'overview']
  },
  {
    id: 'appointments',
    title: 'Appointments',
    description: 'Manage all your appointments',
    path: '/appointments',
    category: 'page' as const,
    icon: '📋',
    keywords: ['bookings', 'schedule']
  }
];

describe('Global Search Functionality', () => {
  const mockHandleSearch = vi.fn();
  const mockHandleSelectResult = vi.fn();
  const mockHandleClose = vi.fn();
  const mockClearSearch = vi.fn();
  const mockClearRecentSearches = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    
    mockUseGlobalSearch.mockReturnValue({
      query: '',
      searchResults: [],
      isOpen: false,
      recentSearches: [],
      handleSearch: mockHandleSearch,
      handleSelectResult: mockHandleSelectResult,
      handleClose: mockHandleClose,
      clearSearch: mockClearSearch,
      clearRecentSearches: mockClearRecentSearches
    });
  });

  it('renders search input correctly', () => {
    render(
      <BrowserRouter>
        <GlobalSearchInput />
      </BrowserRouter>
    );

    const searchInput = screen.getByPlaceholderText(/search or type command/i);
    expect(searchInput).toBeInTheDocument();
  });

  it('calls handleSearch when typing in input', async () => {
    render(
      <BrowserRouter>
        <GlobalSearchInput />
      </BrowserRouter>
    );

    const searchInput = screen.getByPlaceholderText(/search or type command/i);
    fireEvent.change(searchInput, { target: { value: 'dashboard' } });

    expect(mockHandleSearch).toHaveBeenCalledWith('dashboard');
  });

  it('shows search results when query is provided', () => {
    mockUseGlobalSearch.mockReturnValue({
      query: 'dashboard',
      searchResults: mockSearchResults,
      isOpen: true,
      recentSearches: [],
      handleSearch: mockHandleSearch,
      handleSelectResult: mockHandleSelectResult,
      handleClose: mockHandleClose,
      clearSearch: mockClearSearch,
      clearRecentSearches: mockClearRecentSearches
    });

    render(
      <BrowserRouter>
        <GlobalSearchInput />
      </BrowserRouter>
    );

    expect(screen.getByText('Dashboard')).toBeInTheDocument();
    expect(screen.getByText('Appointments')).toBeInTheDocument();
  });

  it('shows recent searches when no query is provided', () => {
    mockUseGlobalSearch.mockReturnValue({
      query: '',
      searchResults: mockSearchResults,
      isOpen: true,
      recentSearches: mockSearchResults,
      handleSearch: mockHandleSearch,
      handleSelectResult: mockHandleSelectResult,
      handleClose: mockHandleClose,
      clearSearch: mockClearSearch,
      clearRecentSearches: mockClearRecentSearches
    });

    render(
      <BrowserRouter>
        <GlobalSearchInput />
      </BrowserRouter>
    );

    expect(screen.getByText('Recent Searches')).toBeInTheDocument();
  });

  it('handles keyboard shortcuts correctly', () => {
    render(
      <BrowserRouter>
        <GlobalSearchInput />
      </BrowserRouter>
    );

    const searchInput = screen.getByPlaceholderText(/search or type command/i);
    
    // Simulate Cmd+K
    fireEvent.keyDown(document, { key: 'k', metaKey: true });
    
    expect(searchInput).toHaveFocus();
  });

  it('clears search when clear button is clicked', () => {
    mockUseGlobalSearch.mockReturnValue({
      query: 'test query',
      searchResults: [],
      isOpen: false,
      recentSearches: [],
      handleSearch: mockHandleSearch,
      handleSelectResult: mockHandleSelectResult,
      handleClose: mockHandleClose,
      clearSearch: mockClearSearch,
      clearRecentSearches: mockClearRecentSearches
    });

    render(
      <BrowserRouter>
        <GlobalSearchInput />
      </BrowserRouter>
    );

    const clearButton = screen.getByLabelText(/clear search/i);
    fireEvent.click(clearButton);

    expect(mockClearSearch).toHaveBeenCalled();
  });
});

describe('Search Algorithm', () => {
  it('should prioritize exact matches', () => {
    // This would test the search algorithm logic
    // Implementation would depend on extracting the search logic to a separate function
    expect(true).toBe(true); // Placeholder
  });

  it('should handle keyword matching', () => {
    // Test keyword matching functionality
    expect(true).toBe(true); // Placeholder
  });

  it('should rank results by relevance', () => {
    // Test result ranking
    expect(true).toBe(true); // Placeholder
  });
});
