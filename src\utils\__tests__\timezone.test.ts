import { 
  localDateTimeToUTC, 
  utcToLocalDateTime, 
  formatLocalDateTime,
  formatLocalTime,
  formatLocalDate,
  getUserTimezone,
  getTimezoneOffset
} from '../timezone';

describe('Timezone Utilities', () => {
  // Mock timezone to EST for consistent testing
  const originalTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
  
  beforeAll(() => {
    // Note: In a real test environment, you might want to mock the timezone
    // For now, we'll test with the current system timezone
  });

  afterAll(() => {
    // Restore original timezone if mocked
  });

  describe('localDateTimeToUTC', () => {
    it('should convert local datetime string to UTC ISO string', () => {
      const localDateTime = '2024-01-15T14:30';
      const result = localDateTimeToUTC(localDateTime);
      
      // The result should be a valid ISO string
      expect(result).toMatch(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/);
      
      // Should be a valid date
      const date = new Date(result);
      expect(date).toBeInstanceOf(Date);
      expect(date.getTime()).not.toBeNaN();
    });

    it('should handle empty string', () => {
      expect(localDateTimeToUTC('')).toBe('');
    });
  });

  describe('utcToLocalDateTime', () => {
    it('should convert UTC ISO string to local datetime string', () => {
      const utcISO = '2024-01-15T19:30:00.000Z';
      const result = utcToLocalDateTime(utcISO);
      
      // Should be in datetime-local format
      expect(result).toMatch(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}$/);
    });

    it('should handle empty string', () => {
      expect(utcToLocalDateTime('')).toBe('');
    });

    it('should round-trip correctly with localDateTimeToUTC', () => {
      const originalLocal = '2024-01-15T14:30';
      const utc = localDateTimeToUTC(originalLocal);
      const backToLocal = utcToLocalDateTime(utc);
      
      expect(backToLocal).toBe(originalLocal);
    });
  });

  describe('formatLocalDateTime', () => {
    it('should format UTC ISO string to local datetime', () => {
      const utcISO = '2024-01-15T19:30:00.000Z';
      const result = formatLocalDateTime(utcISO);
      
      // Should contain date and time elements
      expect(result).toContain('Jan');
      expect(result).toContain('15');
      expect(result).toContain('2024');
      expect(typeof result).toBe('string');
      expect(result.length).toBeGreaterThan(0);
    });

    it('should handle empty string', () => {
      expect(formatLocalDateTime('')).toBe('');
    });

    it('should respect custom formatting options', () => {
      const utcISO = '2024-01-15T19:30:00.000Z';
      const result = formatLocalDateTime(utcISO, {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
      
      expect(result).toContain('January');
      expect(result).toContain('15');
      expect(result).toContain('2024');
    });
  });

  describe('formatLocalTime', () => {
    it('should format UTC ISO string to local time only', () => {
      const utcISO = '2024-01-15T19:30:00.000Z';
      const result = formatLocalTime(utcISO);
      
      // Should contain time elements but not date
      expect(typeof result).toBe('string');
      expect(result.length).toBeGreaterThan(0);
      // Should contain time indicators
      expect(result).toMatch(/\d+:\d+/);
    });

    it('should handle empty string', () => {
      expect(formatLocalTime('')).toBe('');
    });
  });

  describe('formatLocalDate', () => {
    it('should format UTC ISO string to local date only', () => {
      const utcISO = '2024-01-15T19:30:00.000Z';
      const result = formatLocalDate(utcISO);
      
      // Should contain date elements
      expect(result).toContain('Jan');
      expect(result).toContain('15');
      expect(result).toContain('2024');
    });

    it('should handle empty string', () => {
      expect(formatLocalDate('')).toBe('');
    });
  });

  describe('getUserTimezone', () => {
    it('should return a valid timezone string', () => {
      const timezone = getUserTimezone();
      expect(typeof timezone).toBe('string');
      expect(timezone.length).toBeGreaterThan(0);
      // Should be in format like "America/New_York" or "UTC"
      expect(timezone).toMatch(/^[A-Za-z_\/]+$/);
    });
  });

  describe('getTimezoneOffset', () => {
    it('should return a number representing timezone offset', () => {
      const offset = getTimezoneOffset();
      expect(typeof offset).toBe('number');
      // Timezone offset should be between -12 and +14 hours (in minutes)
      expect(offset).toBeGreaterThanOrEqual(-14 * 60);
      expect(offset).toBeLessThanOrEqual(12 * 60);
    });
  });
});
