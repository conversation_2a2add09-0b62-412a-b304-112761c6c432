import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import BusinessLogo from '../components/ui/BusinessLogo';

// Mock the provider hooks
vi.mock('../hooks/useProvider', () => ({
  useHasProviderLogo: vi.fn(),
}));

// Mock the icons
vi.mock('../icons', () => ({
  BoxIcon: ({ className }: { className?: string }) => (
    <div data-testid="box-icon" className={className}>
      BoxIcon
    </div>
  ),
}));

describe('BusinessLogo Component', () => {
  let queryClient: QueryClient;

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false },
      },
    });
    vi.clearAllMocks();
  });

  const renderWithQueryClient = (component: React.ReactElement) => {
    return render(
      <QueryClientProvider client={queryClient}>
        {component}
      </QueryClientProvider>
    );
  };

  it('should render loading state', () => {
    const { useHasProviderLogo } = require('../hooks/useProvider');
    useHasProviderLogo.mockReturnValue({
      hasLogo: false,
      logoUrl: null,
      isLoading: true,
    });

    renderWithQueryClient(<BusinessLogo />);
    
    const loadingElement = document.querySelector('.animate-pulse');
    expect(loadingElement).toBeTruthy();
  });

  it('should render business logo when available', () => {
    const { useHasProviderLogo } = require('../hooks/useProvider');
    const mockLogoUrl = 'https://example.com/business-logo.jpg';
    
    useHasProviderLogo.mockReturnValue({
      hasLogo: true,
      logoUrl: mockLogoUrl,
      isLoading: false,
    });

    renderWithQueryClient(<BusinessLogo />);
    
    const img = screen.getByRole('img');
    expect(img).toBeTruthy();
    expect(img.getAttribute('src')).toBe(mockLogoUrl);
    expect(img.getAttribute('alt')).toBe('Business Logo');
  });

  it('should render fallback icon when no logo', () => {
    const { useHasProviderLogo } = require('../hooks/useProvider');
    
    useHasProviderLogo.mockReturnValue({
      hasLogo: false,
      logoUrl: null,
      isLoading: false,
    });

    renderWithQueryClient(<BusinessLogo />);
    
    const icon = screen.getByTestId('box-icon');
    expect(icon).toBeTruthy();
    expect(icon.className).toContain('w-8 h-8');
  });

  it('should render fallback image when provided and no logo', () => {
    const { useHasProviderLogo } = require('../hooks/useProvider');
    const fallbackSrc = '/images/default-logo.jpg';
    
    useHasProviderLogo.mockReturnValue({
      hasLogo: false,
      logoUrl: null,
      isLoading: false,
    });

    renderWithQueryClient(<BusinessLogo fallbackSrc={fallbackSrc} />);
    
    const img = screen.getByRole('img');
    expect(img).toBeTruthy();
    expect(img.getAttribute('src')).toBe(fallbackSrc);
  });

  it('should apply correct size classes', () => {
    const { useHasProviderLogo } = require('../hooks/useProvider');
    
    useHasProviderLogo.mockReturnValue({
      hasLogo: false,
      logoUrl: null,
      isLoading: false,
    });

    const { container } = renderWithQueryClient(<BusinessLogo size="large" />);
    
    const logoContainer = container.firstChild as HTMLElement;
    expect(logoContainer.className).toContain('w-16 h-16');
  });

  it('should apply correct shape classes', () => {
    const { useHasProviderLogo } = require('../hooks/useProvider');
    
    useHasProviderLogo.mockReturnValue({
      hasLogo: false,
      logoUrl: null,
      isLoading: false,
    });

    const { container } = renderWithQueryClient(<BusinessLogo shape="rounded" />);
    
    const logoContainer = container.firstChild as HTMLElement;
    expect(logoContainer.className).toContain('rounded-full');
  });

  it('should apply custom className', () => {
    const { useHasProviderLogo } = require('../hooks/useProvider');
    
    useHasProviderLogo.mockReturnValue({
      hasLogo: false,
      logoUrl: null,
      isLoading: false,
    });

    const customClass = 'custom-logo-class';
    const { container } = renderWithQueryClient(<BusinessLogo className={customClass} />);
    
    const logoContainer = container.firstChild as HTMLElement;
    expect(logoContainer.className).toContain(customClass);
  });

  it('should use square shape by default', () => {
    const { useHasProviderLogo } = require('../hooks/useProvider');
    
    useHasProviderLogo.mockReturnValue({
      hasLogo: false,
      logoUrl: null,
      isLoading: false,
    });

    const { container } = renderWithQueryClient(<BusinessLogo />);
    
    const logoContainer = container.firstChild as HTMLElement;
    expect(logoContainer.className).toContain('rounded-lg');
  });
});
