/**
 * ServiceService API Tests
 * 
 * Tests for the ServiceService class including:
 * - CRUD operations
 * - API response handling
 * - Error scenarios
 * - Service categories
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { ServiceService } from '../../services/service.service';
import { apiClient } from '../../lib/api-client';
import { Service, ServiceCreateRequest, ServiceCategory } from '../../types';

// Mock the API client
vi.mock('../../lib/api-client', () => ({
  apiClient: {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    delete: vi.fn(),
  },
}));

// Mock the config
vi.mock('../../lib/config', () => ({
  config: {
    endpoints: {
      services: {
        base: '/api/auth/providers/services',
        categories: '/api/auth/providers/service-categories',
      },
    },
  },
}));

const mockService: Service = {
  id: 1,
  sProviderId: 1,
  title: 'General Consultation',
  duration: 30,
  price: 2500,
  pointsRequirements: 1,
  isPublic: true,
  deliveryType: 'at_location',
  servedRegions: [],
  description: 'Standard medical consultation',
  color: '#4CAF50',
  acceptOnline: true,
  acceptNew: true,
  notificationOn: true,
  createdAt: '2024-01-15T10:00:00Z',
  updatedAt: '2024-01-15T10:00:00Z',
};

const mockServiceCategory: ServiceCategory = {
  id: 1,
  title: 'Medical Consultations',
  sProviderId: 1,
};

const mockApiResponse = {
  data: {
    success: true,
    data: mockService,
    message: 'Success',
  },
};

const mockApiListResponse = {
  data: {
    success: true,
    data: [mockService],
    message: 'Success',
  },
};

describe('ServiceService', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('getServices', () => {
    it('fetches services successfully', async () => {
      vi.mocked(apiClient.get).mockResolvedValue(mockApiListResponse);

      const result = await ServiceService.getServices();

      expect(apiClient.get).toHaveBeenCalledWith(
        '/api/auth/providers/services',
        { params: undefined }
      );
      expect(result).toEqual([mockService]);
    });

    it('passes filters correctly', async () => {
      vi.mocked(apiClient.get).mockResolvedValue(mockApiListResponse);
      const filters = { isPublic: true, search: 'consultation' };

      await ServiceService.getServices(filters);

      expect(apiClient.get).toHaveBeenCalledWith(
        '/api/auth/providers/services',
        { params: filters }
      );
    });
  });

  describe('getService', () => {
    it('fetches single service successfully', async () => {
      vi.mocked(apiClient.get).mockResolvedValue(mockApiResponse);

      const result = await ServiceService.getService(1);

      expect(apiClient.get).toHaveBeenCalledWith(
        '/api/auth/providers/services/1'
      );
      expect(result).toEqual(mockService);
    });
  });

  describe('createService', () => {
    it('creates service successfully', async () => {
      vi.mocked(apiClient.post).mockResolvedValue(mockApiResponse);
      
      const serviceData: ServiceCreateRequest = {
        title: 'New Service',
        duration: 60,
        price: 100,
        deliveryType: 'at_location',
        color: '#4CAF50',
      };

      const result = await ServiceService.createService(serviceData);

      expect(apiClient.post).toHaveBeenCalledWith(
        '/api/auth/providers/services',
        serviceData
      );
      expect(result).toEqual(mockService);
    });
  });

  describe('updateService', () => {
    it('updates service successfully', async () => {
      vi.mocked(apiClient.put).mockResolvedValue(mockApiResponse);
      
      const updateData = { title: 'Updated Service' };

      const result = await ServiceService.updateService(1, updateData);

      expect(apiClient.put).toHaveBeenCalledWith(
        '/api/auth/providers/services/1',
        updateData
      );
      expect(result).toEqual(mockService);
    });
  });

  describe('deleteService', () => {
    it('deletes service successfully', async () => {
      const deleteResponse = {
        data: {
          success: true,
          message: 'Service deleted successfully',
        },
      };
      vi.mocked(apiClient.delete).mockResolvedValue(deleteResponse);

      await ServiceService.deleteService(1);

      expect(apiClient.delete).toHaveBeenCalledWith(
        '/api/auth/providers/services/1'
      );
    });

    it('throws error when delete fails', async () => {
      const deleteResponse = {
        data: {
          success: false,
          message: 'Cannot delete service with active appointments',
        },
      };
      vi.mocked(apiClient.delete).mockResolvedValue(deleteResponse);

      await expect(ServiceService.deleteService(1)).rejects.toThrow(
        'Cannot delete service with active appointments'
      );
    });
  });

  describe('Service Categories', () => {
    const mockCategoryResponse = {
      data: {
        success: true,
        data: [mockServiceCategory],
        message: 'Success',
      },
    };

    it('fetches service categories successfully', async () => {
      vi.mocked(apiClient.get).mockResolvedValue(mockCategoryResponse);

      const result = await ServiceService.getServiceCategories();

      expect(apiClient.get).toHaveBeenCalledWith(
        '/api/auth/providers/service-categories'
      );
      expect(result).toEqual([mockServiceCategory]);
    });

    it('creates service category successfully', async () => {
      const categoryResponse = {
        data: {
          success: true,
          data: mockServiceCategory,
          message: 'Success',
        },
      };
      vi.mocked(apiClient.post).mockResolvedValue(categoryResponse);

      const categoryData = { title: 'New Category' };
      const result = await ServiceService.createServiceCategory(categoryData);

      expect(apiClient.post).toHaveBeenCalledWith(
        '/api/auth/providers/service-categories',
        categoryData
      );
      expect(result).toEqual(mockServiceCategory);
    });

    it('updates service category successfully', async () => {
      const categoryResponse = {
        data: {
          success: true,
          data: mockServiceCategory,
          message: 'Success',
        },
      };
      vi.mocked(apiClient.put).mockResolvedValue(categoryResponse);

      const updateData = { title: 'Updated Category' };
      const result = await ServiceService.updateServiceCategory(1, updateData);

      expect(apiClient.put).toHaveBeenCalledWith(
        '/api/auth/providers/service-categories/1',
        updateData
      );
      expect(result).toEqual(mockServiceCategory);
    });

    it('deletes service category successfully', async () => {
      const deleteResponse = {
        data: {
          success: true,
          message: 'Category deleted successfully',
        },
      };
      vi.mocked(apiClient.delete).mockResolvedValue(deleteResponse);

      await ServiceService.deleteServiceCategory(1);

      expect(apiClient.delete).toHaveBeenCalledWith(
        '/api/auth/providers/service-categories/1'
      );
    });
  });

  describe('Error Handling', () => {
    it('handles network errors', async () => {
      const networkError = new Error('Network Error');
      vi.mocked(apiClient.get).mockRejectedValue(networkError);

      await expect(ServiceService.getServices()).rejects.toThrow('Network Error');
    });

    it('handles API errors', async () => {
      const apiError = {
        response: {
          status: 400,
          data: {
            success: false,
            message: 'Validation failed',
            errors: [
              { field: 'title', message: 'Title is required' }
            ],
          },
        },
      };
      vi.mocked(apiClient.post).mockRejectedValue(apiError);

      const serviceData: ServiceCreateRequest = {
        title: '',
        duration: 60,
        price: 100,
        deliveryType: 'at_location',
        color: '#4CAF50',
      };

      await expect(ServiceService.createService(serviceData)).rejects.toEqual(apiError);
    });
  });
});
