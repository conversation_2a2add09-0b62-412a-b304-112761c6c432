/**
 * Tests for profile completion calculation utilities
 */

import { calculateProfileCompletion, calculateProfileCompletionSafe } from '../profile-completion';
import { ProfileCompletionData } from '../../types/profile-completion';

// Mock user data
const mockUser = {
  id: 'user-123',
  email: '<EMAIL>',
  firstName: 'John',
  lastName: 'Doe',
  role: 'CLIENT' as const,
  isEmailVerified: true,
  isPhoneVerified: true,
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z',
};

// Mock provider with no data (0% completion)
const mockEmptyProvider = {
  id: 1,
  userId: 'user-123',
  isSetupComplete: false,
  isVerified: false,
  averageRating: 0,
  totalReviews: 0,
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z',
  providingPlaces: [],
  services: [],
  queues: [],
};

// Mock provider with complete data (100% completion)
const mockCompleteProvider = {
  id: 1,
  userId: 'user-123',
  title: 'Test Business',
  phone: '+123456789',
  presentation: 'Test business description',
  providerCategoryId: 1,
  logoId: 'logo-123',
  isSetupComplete: false,
  isVerified: false,
  averageRating: 4.5,
  totalReviews: 10,
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z',
  category: {
    id: 1,
    title: 'Test Category',
    description: 'Test category description',
    isActive: true,
    sortOrder: 0,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  },
  logo: {
    id: 'logo-123',
    filename: 'logo.png',
    originalName: 'business-logo.png',
    mimeType: 'image/png',
    size: 1024,
    url: 'https://example.com/logo.png',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  },
  providingPlaces: [
    {
      id: 1,
      sProviderId: 1,
      name: 'Main Location',
      address: '123 Test Street',
      city: 'Test City',
      shortName: 'Main',
      mobile: '+123456789',
      isMobileHidden: false,
      parking: true,
      elevator: false,
      handicapAccess: true,
      timezone: 'UTC',
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z',
    },
  ],
  services: [
    {
      id: 1,
      sProviderId: 1,
      title: 'Test Service',
      duration: 30,
      price: 50,
      pointsRequirements: 1,
      isPublic: true,
      deliveryType: 'at_location' as const,
      servedRegions: ['Test City'],
      description: 'Test service description',
      color: '#3B82F6',
      acceptOnline: true,
      acceptNew: true,
      notificationOn: true,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z',
    },
  ],
  queues: [
    {
      id: 1,
      sProviderId: 1,
      sProvidingPlaceId: 1,
      title: 'Test Queue',
      isActive: true,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z',
    },
  ],
};

describe('calculateProfileCompletion', () => {
  test('should return 0% for empty provider', () => {
    const data: ProfileCompletionData = {
      user: mockUser,
      provider: mockEmptyProvider,
    };

    const result = calculateProfileCompletion(data);

    expect(result.overallPercentage).toBe(0);
    expect(result.overallCompleted).toBe(false);
    expect(result.shouldMarkAsComplete).toBe(false);
    expect(result.breakdown.profilePicture.completed).toBe(false);
    expect(result.breakdown.providerInfo.completed).toBe(false);
    expect(result.breakdown.providingPlaces.completed).toBe(false);
    expect(result.breakdown.services.completed).toBe(false);
    expect(result.breakdown.queues.completed).toBe(false);
  });

  test('should return 100% for complete provider', () => {
    const data: ProfileCompletionData = {
      user: mockUser,
      provider: mockCompleteProvider,
    };

    const result = calculateProfileCompletion(data);

    expect(result.overallPercentage).toBe(100);
    expect(result.overallCompleted).toBe(true);
    expect(result.shouldMarkAsComplete).toBe(true);
    expect(result.breakdown.profilePicture.completed).toBe(true);
    expect(result.breakdown.providerInfo.completed).toBe(true);
    expect(result.breakdown.providingPlaces.completed).toBe(true);
    expect(result.breakdown.services.completed).toBe(true);
    expect(result.breakdown.queues.completed).toBe(true);
  });

  test('should calculate partial completion correctly', () => {
    const partialProvider = {
      ...mockEmptyProvider,
      title: 'Test Business',
      phone: '+123456789',
      presentation: 'Test description',
      providerCategoryId: 1,
      // Missing logo, locations, services, and queues
    };

    const data: ProfileCompletionData = {
      user: mockUser,
      provider: partialProvider,
    };

    const result = calculateProfileCompletion(data);

    // Should have 30% from provider info only
    expect(result.overallPercentage).toBe(30);
    expect(result.overallCompleted).toBe(false);
    expect(result.breakdown.providerInfo.completed).toBe(true);
    expect(result.breakdown.profilePicture.completed).toBe(false);
    expect(result.breakdown.providingPlaces.completed).toBe(false);
    expect(result.breakdown.services.completed).toBe(false);
    expect(result.breakdown.queues.completed).toBe(false);
  });

  test('should handle provider info with missing fields', () => {
    const partialInfoProvider = {
      ...mockEmptyProvider,
      title: 'Test Business',
      phone: '+123456789',
      // Missing presentation and category
    };

    const data: ProfileCompletionData = {
      user: mockUser,
      provider: partialInfoProvider,
    };

    const result = calculateProfileCompletion(data);

    expect(result.breakdown.providerInfo.completed).toBe(false);
    expect(result.breakdown.providerInfo.percentage).toBe(50); // 2 out of 4 fields
    expect(result.breakdown.providerInfo.requiredFields.title).toBe(true);
    expect(result.breakdown.providerInfo.requiredFields.phone).toBe(true);
    expect(result.breakdown.providerInfo.requiredFields.presentation).toBe(false);
    expect(result.breakdown.providerInfo.requiredFields.category).toBe(false);
  });

  test('should handle locations with incomplete information', () => {
    const incompleteLocationProvider = {
      ...mockEmptyProvider,
      providingPlaces: [
        {
          id: 1,
          sProviderId: 1,
          name: 'Test Location',
          // Missing address and city
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z',
        },
      ],
    };

    const data: ProfileCompletionData = {
      user: mockUser,
      provider: incompleteLocationProvider,
    };

    const result = calculateProfileCompletion(data);

    expect(result.breakdown.providingPlaces.completed).toBe(false);
    expect(result.breakdown.providingPlaces.percentage).toBe(20); // Minimum credit
    expect(result.breakdown.providingPlaces.count).toBe(1);
    expect(result.breakdown.providingPlaces.validPlaces).toBe(0);
  });

  test('should handle inactive queues', () => {
    const inactiveQueueProvider = {
      ...mockEmptyProvider,
      queues: [
        {
          id: 1,
          sProviderId: 1,
          sProvidingPlaceId: 1,
          title: 'Inactive Queue',
          isActive: false,
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z',
        },
      ],
    };

    const data: ProfileCompletionData = {
      user: mockUser,
      provider: inactiveQueueProvider,
    };

    const result = calculateProfileCompletion(data);

    expect(result.breakdown.queues.completed).toBe(false);
    expect(result.breakdown.queues.percentage).toBe(0);
    expect(result.breakdown.queues.count).toBe(1);
    expect(result.breakdown.queues.activeQueues).toBe(0);
  });

  test('should generate appropriate next steps', () => {
    const data: ProfileCompletionData = {
      user: mockUser,
      provider: mockEmptyProvider,
    };

    const result = calculateProfileCompletion(data);

    expect(result.nextSteps.length).toBeGreaterThan(0);
    expect(result.nextSteps).toContain(expect.stringContaining('provider information'));
    expect(result.nextSteps).toContain(expect.stringContaining('business location'));
    expect(result.nextSteps).toContain(expect.stringContaining('service offering'));
  });

  test('should identify critical missing items', () => {
    const data: ProfileCompletionData = {
      user: mockUser,
      provider: mockEmptyProvider,
    };

    const result = calculateProfileCompletion(data);

    expect(result.criticalMissing).toContain('Business information');
    expect(result.criticalMissing).toContain('Business locations');
    expect(result.criticalMissing).toContain('Service offerings');
  });

  test('should handle 80% completion threshold', () => {
    const almostCompleteProvider = {
      ...mockCompleteProvider,
      queues: [], // Remove queues to drop below 100%
    };

    const data: ProfileCompletionData = {
      user: mockUser,
      provider: almostCompleteProvider,
    };

    const result = calculateProfileCompletion(data);

    expect(result.overallPercentage).toBe(85); // 100 - 15 (queues weight)
    expect(result.overallCompleted).toBe(true); // >= 80%
    expect(result.shouldMarkAsComplete).toBe(false); // Not 100%
  });
});

describe('calculateProfileCompletionSafe', () => {
  test('should handle null/undefined data gracefully', () => {
    const result1 = calculateProfileCompletionSafe({});
    const result2 = calculateProfileCompletionSafe({ user: mockUser });
    const result3 = calculateProfileCompletionSafe({ provider: mockEmptyProvider });

    [result1, result2, result3].forEach(result => {
      expect(result.overallPercentage).toBe(0);
      expect(result.overallCompleted).toBe(false);
      expect(result.nextSteps).toContain('Complete your provider profile setup');
      expect(result.criticalMissing).toContain('Unable to calculate completion status');
    });
  });

  test('should handle malformed data without throwing', () => {
    const malformedData = {
      user: { invalid: 'data' },
      provider: { also: 'invalid' },
    };

    expect(() => {
      calculateProfileCompletionSafe(malformedData as any);
    }).not.toThrow();
  });
});
