import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router';
import { useActiveAppointment } from '../../hooks/useAppointments';
import Button from '../ui/button/Button';
import { formatLocalTime } from '../../utils/timezone';

const ActiveSessionWidget: React.FC = () => {
  const navigate = useNavigate();
  const { data: activeAppointment, isLoading } = useActiveAppointment();
  const [timeRemaining, setTimeRemaining] = useState<number>(0);
  const [sessionStartTime, setSessionStartTime] = useState<Date | null>(null);

  // Calculate time remaining when appointment loads
  useEffect(() => {
    if (activeAppointment) {
      const duration = activeAppointment.serviceDuration || activeAppointment.service?.duration || 30;
      const startTime = activeAppointment.realAppointmentStartTime
        ? new Date(activeAppointment.realAppointmentStartTime)
        : new Date();

      setSessionStartTime(startTime);
      const endTime = new Date(startTime.getTime() + duration * 60000);
      const remaining = Math.max(0, endTime.getTime() - Date.now());
      setTimeRemaining(Math.floor(remaining / 1000));
    }
  }, [activeAppointment]);

  // Timer countdown effect
  useEffect(() => {
    let interval: NodeJS.Timeout;

    if (activeAppointment && timeRemaining > 0) {
      interval = setInterval(() => {
        setTimeRemaining(prev => Math.max(0, prev - 1));
      }, 1000);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [activeAppointment, timeRemaining]);

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const handleContinueSession = () => {
    if (activeAppointment) {
      navigate(`/service-session/${activeAppointment.id}`);
    }
  };

  if (isLoading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-4"></div>
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-2/3 mb-2"></div>
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
        </div>
      </div>
    );
  }

  if (!activeAppointment) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
        <div className="text-center">
          <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
            <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            No Active Session
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
            Start an appointment to begin a service session
          </p>
          <Button
            onClick={() => navigate('/appointments')}
            variant="outline"
            size="sm"
          >
            View Appointments
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          Active Session
        </h3>
        <div className="flex items-center gap-2">
          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
          <span className="text-sm text-green-600 dark:text-green-400 font-medium">Live</span>
        </div>
      </div>

      <div className="space-y-4">
        {/* Timer Display */}
        <div className="text-center">
          <div className={`text-3xl font-mono font-bold ${
            timeRemaining <= 300 ? 'text-red-600' : 
            timeRemaining <= 600 ? 'text-yellow-600' : 
            'text-green-600'
          }`}>
            {formatTime(timeRemaining)}
          </div>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            Time Remaining
          </p>
        </div>

        {/* Customer Info */}
        <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
          <div className="flex items-center gap-3 mb-3">
            <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-medium text-sm">
              {activeAppointment.customer?.firstName?.charAt(0)}{activeAppointment.customer?.lastName?.charAt(0)}
            </div>
            <div>
              <p className="font-medium text-gray-900 dark:text-white text-sm">
                {activeAppointment.customer?.firstName} {activeAppointment.customer?.lastName}
              </p>
              <p className="text-xs text-gray-600 dark:text-gray-400">
                {activeAppointment.service?.title}
              </p>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-600 dark:text-gray-400">Started:</span>
              <p className="font-medium text-gray-900 dark:text-white">
                {sessionStartTime ? formatLocalTime(sessionStartTime.toISOString()) : 'Now'}
              </p>
            </div>
            <div>
              <span className="text-gray-600 dark:text-gray-400">Duration:</span>
              <p className="font-medium text-gray-900 dark:text-white">
                {activeAppointment.service?.duration || activeAppointment.serviceDuration} min
              </p>
            </div>
          </div>
        </div>

        {/* Action Button */}
        <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
          <Button
            onClick={handleContinueSession}
            className="w-full bg-brand-600 hover:bg-brand-700 text-white"
            size="sm"
          >
            Continue Session
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ActiveSessionWidget;
