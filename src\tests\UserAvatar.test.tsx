import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import UserAvatar from '../components/ui/UserAvatar';

// Mock the user hooks
vi.mock('../hooks/useUser', () => ({
  useHasProfilePicture: vi.fn(),
}));

// Mock the icons
vi.mock('../icons', () => ({
  UserCircleIcon: ({ className }: { className?: string }) => (
    <div data-testid="user-circle-icon" className={className}>
      UserIcon
    </div>
  ),
}));

describe('UserAvatar Component', () => {
  let queryClient: QueryClient;

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false },
      },
    });
    vi.clearAllMocks();
  });

  const renderWithQueryClient = (component: React.ReactElement) => {
    return render(
      <QueryClientProvider client={queryClient}>
        {component}
      </QueryClientProvider>
    );
  };

  it('should render loading state', () => {
    const { useHasProfilePicture } = require('../hooks/useUser');
    useHasProfilePicture.mockReturnValue({
      hasProfilePicture: false,
      profilePictureUrl: null,
      isLoading: true,
    });

    renderWithQueryClient(<UserAvatar />);
    
    const loadingElement = document.querySelector('.animate-pulse');
    expect(loadingElement).toBeTruthy();
  });

  it('should render profile picture when available', () => {
    const { useHasProfilePicture } = require('../hooks/useUser');
    const mockProfileUrl = 'https://example.com/profile.jpg';
    
    useHasProfilePicture.mockReturnValue({
      hasProfilePicture: true,
      profilePictureUrl: mockProfileUrl,
      isLoading: false,
    });

    renderWithQueryClient(<UserAvatar />);
    
    const img = screen.getByRole('img');
    expect(img).toBeTruthy();
    expect(img.getAttribute('src')).toBe(mockProfileUrl);
    expect(img.getAttribute('alt')).toBe('User Profile');
  });

  it('should render fallback icon when no profile picture', () => {
    const { useHasProfilePicture } = require('../hooks/useUser');
    
    useHasProfilePicture.mockReturnValue({
      hasProfilePicture: false,
      profilePictureUrl: null,
      isLoading: false,
    });

    renderWithQueryClient(<UserAvatar />);
    
    const icon = screen.getByTestId('user-circle-icon');
    expect(icon).toBeTruthy();
    expect(icon.className).toContain('w-8 h-8');
  });

  it('should render fallback image when provided and no profile picture', () => {
    const { useHasProfilePicture } = require('../hooks/useUser');
    const fallbackSrc = '/images/default-avatar.jpg';
    
    useHasProfilePicture.mockReturnValue({
      hasProfilePicture: false,
      profilePictureUrl: null,
      isLoading: false,
    });

    renderWithQueryClient(<UserAvatar fallbackSrc={fallbackSrc} />);
    
    const img = screen.getByRole('img');
    expect(img).toBeTruthy();
    expect(img.getAttribute('src')).toBe(fallbackSrc);
  });

  it('should apply correct size classes', () => {
    const { useHasProfilePicture } = require('../hooks/useUser');
    
    useHasProfilePicture.mockReturnValue({
      hasProfilePicture: false,
      profilePictureUrl: null,
      isLoading: false,
    });

    const { container } = renderWithQueryClient(<UserAvatar size="large" />);
    
    const avatarContainer = container.firstChild as HTMLElement;
    expect(avatarContainer.className).toContain('w-16 h-16');
  });

  it('should show online status when enabled', () => {
    const { useHasProfilePicture } = require('../hooks/useUser');
    
    useHasProfilePicture.mockReturnValue({
      hasProfilePicture: false,
      profilePictureUrl: null,
      isLoading: false,
    });

    const { container } = renderWithQueryClient(<UserAvatar showOnlineStatus />);
    
    const statusIndicator = container.querySelector('.bg-green-500');
    expect(statusIndicator).toBeTruthy();
  });

  it('should apply custom className', () => {
    const { useHasProfilePicture } = require('../hooks/useUser');
    
    useHasProfilePicture.mockReturnValue({
      hasProfilePicture: false,
      profilePictureUrl: null,
      isLoading: false,
    });

    const customClass = 'custom-avatar-class';
    const { container } = renderWithQueryClient(<UserAvatar className={customClass} />);
    
    const avatarContainer = container.firstChild as HTMLElement;
    expect(avatarContainer.className).toContain(customClass);
  });
});
