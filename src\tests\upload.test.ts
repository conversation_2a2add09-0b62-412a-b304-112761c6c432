import { describe, it, expect, vi, beforeEach } from 'vitest';
import { validateUploadFile, uploadToS3, performS3Upload } from '../utils/s3-upload.utils';
import { UserService } from '../services/user.service';
import { ProviderService } from '../services/provider.service';

// Mock the API client
vi.mock('../lib/api-client', () => ({
  apiClient: {
    get: vi.fn(),
    post: vi.fn(),
    delete: vi.fn(),
  },
}));

// Mock config
vi.mock('../lib/config', () => ({
  config: {
    endpoints: {
      user: {
        profilePicture: '/api/auth/user/profile-picture',
      },
      provider: {
        logo: '/api/auth/provider/logo',
      },
    },
  },
}));

describe('Upload Functionality', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('File Validation', () => {
    it('should accept valid JPEG files', () => {
      const file = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
      const result = validateUploadFile(file);
      expect(result.isValid).toBe(true);
    });

    it('should accept valid PNG files', () => {
      const file = new File(['test'], 'test.png', { type: 'image/png' });
      const result = validateUploadFile(file);
      expect(result.isValid).toBe(true);
    });

    it('should reject invalid file types', () => {
      const file = new File(['test'], 'test.txt', { type: 'text/plain' });
      const result = validateUploadFile(file);
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('Only JPEG and PNG images are allowed');
    });

    it('should reject files that are too large', () => {
      const largeFile = new File(['x'.repeat(11 * 1024 * 1024)], 'large.jpg', { 
        type: 'image/jpeg' 
      });
      const result = validateUploadFile(largeFile);
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('File size must be less than 10MB');
    });
  });

  describe('S3 Upload', () => {
    it('should create proper FormData for S3 upload', async () => {
      const mockXHR = {
        upload: { addEventListener: vi.fn() },
        addEventListener: vi.fn(),
        open: vi.fn(),
        send: vi.fn(),
        status: 200,
      };

      // Mock XMLHttpRequest
      global.XMLHttpRequest = vi.fn(() => mockXHR) as any;

      const file = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
      const uploadFields = {
        key: 'test-key',
        'Content-Type': 'image/jpeg',
        policy: 'test-policy',
      };

      // Simulate successful upload
      setTimeout(() => {
        const loadHandler = mockXHR.addEventListener.mock.calls.find(
          call => call[0] === 'load'
        )?.[1];
        if (loadHandler) loadHandler();
      }, 0);

      await expect(
        uploadToS3('https://test-bucket.s3.amazonaws.com', uploadFields, file)
      ).resolves.toBeUndefined();

      expect(mockXHR.open).toHaveBeenCalledWith('POST', 'https://test-bucket.s3.amazonaws.com');
      expect(mockXHR.send).toHaveBeenCalled();
    });
  });

  describe('User Service', () => {
    it('should generate profile picture upload URL', async () => {
      const mockResponse = {
        data: {
          success: true,
          data: {
            uploadUrl: 'https://test-bucket.s3.amazonaws.com',
            uploadFields: { key: 'test-key' },
            file: { id: 'file-id', name: 'test.jpg' },
            user: { id: 'user-id', profilePictureId: 'file-id' },
          },
        },
      };

      const { apiClient } = await import('../lib/api-client');
      (apiClient.post as any).mockResolvedValue(mockResponse);

      const result = await UserService.generateProfilePictureUploadUrl('test.jpg', 'image/jpeg');

      expect(result.uploadUrl).toBe('https://test-bucket.s3.amazonaws.com');
      expect(result.file.name).toBe('test.jpg');
      expect(apiClient.post).toHaveBeenCalledWith(
        '/api/auth/user/profile-picture',
        { fileName: 'test.jpg', fileType: 'image/jpeg' }
      );
    });
  });

  describe('Provider Service', () => {
    it('should generate logo upload URL', async () => {
      const mockResponse = {
        data: {
          success: true,
          data: {
            uploadUrl: 'https://test-bucket.s3.amazonaws.com',
            uploadFields: { key: 'test-key' },
            file: { id: 'file-id', name: 'logo.png' },
            provider: { id: 'provider-id', logoId: 'file-id' },
          },
        },
      };

      const { apiClient } = await import('../lib/api-client');
      (apiClient.post as any).mockResolvedValue(mockResponse);

      const result = await ProviderService.generateLogoUploadUrl('logo.png', 'image/png');

      expect(result.uploadUrl).toBe('https://test-bucket.s3.amazonaws.com');
      expect(result.file.name).toBe('logo.png');
      expect(apiClient.post).toHaveBeenCalledWith(
        '/api/auth/provider/logo',
        { fileName: 'logo.png', fileType: 'image/png' }
      );
    });
  });

  describe('Complete Upload Workflow', () => {
    it('should perform complete S3 upload workflow', async () => {
      const file = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
      const mockGenerateUrl = vi.fn().mockResolvedValue({
        uploadUrl: 'https://test-bucket.s3.amazonaws.com',
        uploadFields: { key: 'test-key' },
        file: { id: 'file-id', name: 'test.jpg' },
      });

      // Mock successful S3 upload
      const mockXHR = {
        upload: { addEventListener: vi.fn() },
        addEventListener: vi.fn(),
        open: vi.fn(),
        send: vi.fn(),
        status: 200,
      };
      global.XMLHttpRequest = vi.fn(() => mockXHR) as any;

      // Simulate successful upload
      setTimeout(() => {
        const loadHandler = mockXHR.addEventListener.mock.calls.find(
          call => call[0] === 'load'
        )?.[1];
        if (loadHandler) loadHandler();
      }, 0);

      const progressCallback = vi.fn();
      const result = await performS3Upload(mockGenerateUrl, {
        file,
        onProgress: progressCallback,
      });

      expect(result.success).toBe(true);
      expect(result.fileData.name).toBe('test.jpg');
      expect(mockGenerateUrl).toHaveBeenCalledWith('test.jpg', 'image/jpeg');
      expect(progressCallback).toHaveBeenCalledWith(100);
    });
  });
});
